package com.ruoyi.ysf.mapper;

import com.ruoyi.ysf.entity.YsfStoDpsSub;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.shucheng.modes.dto.DispensingRecordDetailQueryDto;

import java.util.List;

/**
 * <p>
 * 发药单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface YsfStoDpsSubMapper extends BaseMapper<YsfStoDpsSub> {

    List<YsfStoDpsSub> getSendStatusList();

    /**
     * 根据条件查询发药单明细列表
     *
     * @param queryDto 查询参数
     * @return 发药单明细列表
     */
    List<YsfStoDpsSub> queryDispensingRecordDetailsWithConditions(DispensingRecordDetailQueryDto queryDto);
}
