package com.ruoyi.shucheng.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.aop.aspect.SaasAuthorizationVerifyAspect;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.his.domain.PrescriptionTrackingLog;
import com.ruoyi.his.service.IPrescriptionTrackingLogService;
import com.ruoyi.nhsa.util.saas.response.user.SaasUserInfoResponse;
import com.ruoyi.shucheng.enums.DispenseOrderStatusEnum;
import com.ruoyi.shucheng.enums.SdTcStatusEnum;
import com.ruoyi.shucheng.enums.TaskStatusEnum;
import com.ruoyi.shucheng.modes.dto.DispensingRecordDetailQueryDto;
import com.ruoyi.shucheng.modes.dto.DispensingRecordQueryDto;
import com.ruoyi.shucheng.modes.dto.PatInfoDto;
import com.ruoyi.shucheng.modes.dto.TraceabilityUploadDto;
import com.ruoyi.shucheng.modes.vo.DispensingRecordDetailVo;
import com.ruoyi.shucheng.modes.vo.DispensingRecordVo;
import com.ruoyi.shucheng.modes.vo.TraceabilityUploadResultVo;
import com.ruoyi.yingdong.domain.HisDrugDict;
import com.ruoyi.yingdong.mapper.YingdongDeptMapper;
import com.ruoyi.yingdong.msunhis.request.DrugDispenseInfoDetailsRequest;
import com.ruoyi.yingdong.msunhis.request.DrugDispenseInfosRequest;
import com.ruoyi.yingdong.msunhis.response.*;
import com.ruoyi.yingdong.msunhis.util.MsunYingDongUtil;
import com.ruoyi.yingdong.service.IHisDrugDictService;
import com.ruoyi.ysf.entity.*;
import com.ruoyi.ysf.enums.StoTcTaskStatusEnum;
import com.ruoyi.ysf.mapper.YsfStoTcTaskMapper;
import com.ruoyi.ysf.mapper.YsfStoDpsSubMapper;
import com.ruoyi.ysf.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 舒城人医服务实现类
 *
 * <AUTHOR>
 * @date 2025-12-04
 */
@Service
@Slf4j
public class ShuChengRenYiService {

    @Resource
    private IHisDrugDictService hisDrugDictService;
    @Resource
    private ShuChengDispenseService shuChengDispenseService;

    @Resource
    private YingdongDeptMapper yingdongDeptMapper;

    @Resource
    private IPrescriptionTrackingLogService prescriptionTrackingLogService;

    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;

    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;

    @Resource
    private YsfStoDpsService ysfStoDpsService;

    @Resource
    private YsfStoDpsSubService ysfStoDpsSubService;

    @Resource
    private YsfStoTcService ysfStoTcService;

    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;

    @Resource
    private YsfStoDpsSubMapper ysfStoDpsSubMapper;

    /**
     * 查询药品处方信息
     *
     * @param request 查询请求参数
     * @return 药品处方信息列表
     */
    public List<DrugDispenseInfoResponse> queryDrugDispenseInfos(DrugDispenseInfosRequest request) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        // 获取医院ID
        String orgId = userInfo.getUser()
                .getOrgId();
        String hospitalId = null;
        if (MsunYingDongUtil.MEDICAL_CODE.equals(orgId)) {
            // 如果orgId是医疗机构代码，则直接返回空列表
            hospitalId = MsunYingDongUtil.HOSPITAL_ID;
        }

        if (StringUtils.isEmpty(hospitalId)) {
            return Collections.emptyList();
        }

        // 如果没有患者ID但有卡号，则根据卡号查询患者ID
        if (request.getPatId() == null && StringUtils.isNotEmpty(request.getCardNo())) {
            PatInfosFindResponse patInfos = this.queryPatInfosByCardNo(request.getCardNo(), "3");
            request.setPatId(Long.valueOf(patInfos.getPatId()));
            log.info("根据卡号查询到患者ID：{}", patInfos.getPatId());
        }

        // 调用远程接口获取处方信息
        MsunCommonResponse<DrugDispenseInfoResponse> response = MsunYingDongUtil.drugDispenseInfos(request);
        List<DrugDispenseInfoResponse> dataList = response.getData();

        // 处方信息按开具时间倒序排序
        if (!ObjectUtils.isEmpty(dataList)) {
            sortDrugDispenseInfoByTime(dataList);

            // 数据增强：关联药品追溯码任务状态
            enhanceDrugDispenseInfosWithTaskStatus(dataList);
        }

        return dataList;
    }

    /**
     * 查询药品处方明细信息
     *
     * @param request 处方明细查询请求参数
     * @return 药品处方明细信息列表
     */
    public List<DrugDispenseInfoDetailResponse> queryDrugDispenseInfoDetails(DrugDispenseInfoDetailsRequest request) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        // 获取医院ID
        String orgId = userInfo.getUser()
                .getOrgId();
        String hospitalId = null;
        if (MsunYingDongUtil.MEDICAL_CODE.equals(orgId)) {
            // 如果orgId是医疗机构代码，则直接返回空列表
            hospitalId = MsunYingDongUtil.HOSPITAL_ID;
        }

        // 调用远程接口获取处方明细信息
        MsunCommonResponse<DrugDispenseInfoDetailResponse> response = MsunYingDongUtil.drugDispenseInfoDetails(request);
        List<DrugDispenseInfoDetailResponse> details = response.getData();

        // 没有数据则直接返回
        if (ObjectUtils.isEmpty(details)) {
            return details;
        }

        // 处理药品字典信息和匹配码
        processDrugDictAndTrackingInfo(details);

        return details;
    }

    /**
     * 根据卡号查询患者信息
     *
     * @param cardNo 卡号
     * @param type   查询类型，默认为3（就诊卡号）
     * @return 患者信息列表
     */
    public PatInfosFindResponse queryPatInfosByCardNo(String cardNo, String type) {
        // 构建查询请求
        PatInfoDto patInfoDto = new PatInfoDto();
        patInfoDto.setCardNo(cardNo);
        patInfoDto.setType(StringUtils.isEmpty(type) ? "3" : type);

        // 调用远程接口查询患者信息
        MsunDataResponse<PatInfosFindResponse> response = MsunYingDongUtil.patInfosFind(patInfoDto);
        if (response.getData() == null) {
            throw new ServiceException("查询患者信息返回数据为空");
        }
        return response.getData();
    }

    /**
     * 按照处方开具时间倒序排序
     *
     * @param dataList 处方信息列表
     */
    private void sortDrugDispenseInfoByTime(List<DrugDispenseInfoResponse> dataList) {
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 使用自定义的比较器进行排序
        Collections.sort(dataList, (d1, d2) -> {
            // 将字符串时间转换为LocalDateTime
            LocalDateTime time1 = LocalDateTime.parse(d1.getPrescribeTime(), formatter);
            LocalDateTime time2 = LocalDateTime.parse(d2.getPrescribeTime(), formatter);
            // 进行降序比较
            return time2.compareTo(time1);
        });
    }

    /**
     * 处理药品字典信息和处方追踪记录
     *
     * @param details 处方明细信息列表
     */
    private void processDrugDictAndTrackingInfo(List<DrugDispenseInfoDetailResponse> details) {
        if (ObjectUtils.isEmpty(details)) {
            return;
        }

        // 获取所有药品ID
        List<String> drugIds = details.stream()
                .map(DrugDispenseInfoDetailResponse::getDrugBasicMainId)
                .distinct()
                .collect(Collectors.toList());

        // 并行查询药品字典信息
        ConcurrentMap<String, HisDrugDict> drugIdToHisDrugJsrmyyMap = new ConcurrentHashMap<>();
        drugIds.parallelStream()
                .forEach(drugId -> {
                    HisDrugDict hisDrugDict = hisDrugDictService.getByDrugId(drugId);
                    if (!ObjectUtils.isEmpty(hisDrugDict)) {
                        drugIdToHisDrugJsrmyyMap.put(drugId, hisDrugDict);
                    }
                });

        // 首先获取处方ID，查询有没有关联的任务ID
        String outPresId = details.get(0)
                .getOutPresId();
        if (StringUtils.isEmpty(outPresId)) {
            log.warn("处方ID为空，无法查询关联任务");
            return;
        }

        // 查询关联的任务信息
        LambdaQueryWrapper<YsfStoTcTask> taskQuery = new LambdaQueryWrapper<>();
        taskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                .eq(YsfStoTcTask::getDelFlag, "0")
                .orderByDesc(YsfStoTcTask::getCreateTime);
        YsfStoTcTask task = ysfStoTcTaskMapper.selectOne(taskQuery);

        // 处理每一条处方明细信息
        details.forEach(detail -> {
            // 添加药品字典信息
            String drugId = detail.getDrugBasicMainId();
            HisDrugDict hisDrugJsrmyy = drugIdToHisDrugJsrmyyMap.get(drugId);
            if (hisDrugJsrmyy != null) {
                detail.setHisDrugDict(hisDrugJsrmyy);
            } else {
                // 没从药品字典表获取药品数据，就从众阳his药品接口单独查询该药品code的信息，然后保存到药品字典表
                // 调用复用方法获取并保存药品字典
                HisDrugDict hisDrugDict = shuChengDispenseService.fetchAndSaveDrugDict(drugId, null);
                if (hisDrugDict != null) {
                    detail.setHisDrugDict(hisDrugDict);
                }
            }

            // 获取并处理处方追踪日志信息
            try {
                List<PrescriptionTrackingLog> listByDispenseId = prescriptionTrackingLogService.getListByDispenseId(detail.getDispenseId());
                Set<String> codeList = new HashSet<>();
                for (PrescriptionTrackingLog trackingLog : listByDispenseId) {
                    String matchedCodes = trackingLog.getMatchedCodes();
                    if (!ObjectUtils.isEmpty(matchedCodes)) {
                        String[] split = matchedCodes.split(",");
                        if (!ObjectUtils.isEmpty(split)) {
                            codeList.addAll(Arrays.asList(split));
                        }
                    }
                }
                detail.setCodeList(new ArrayList<>(codeList));
            } catch (Exception e) {
                log.error("处理处方追踪日志异常: {}", e.getMessage(), e);
            }

            // 如果处方明细有关联的任务，查询追溯码信息
            if (task != null && task.getIdTask() != null && StringUtils.isNotEmpty(detail.getOutPresdetailid())) {
                try {
                    // 查询该明细对应的任务明细记录
                    YsfStoTcTaskSub taskSub = ysfStoTcTaskSubService.getByTaskIdAndCfmxxh(
                            task.getIdTask()
                                    .toString(), detail.getOutPresdetailid());

                    if (taskSub != null) {
                        // 设置追溯码相关信息
                        detail.setTaskIdSubDps(taskSub.getIdSub() != null ?
                                String.valueOf(taskSub.getIdSub()) : null);
                        detail.setTaskDrugtracinfoDps(taskSub.getDrugtracinfo());
                        detail.setTaskFgScannedDps(taskSub.getFgScanned());
                        // 扫描时间格式化
                        if (taskSub.getScanTime() != null) {
                            detail.setTaskDetailScanTimeDps(taskSub.getScanTime()
                                    .toString());
                        }
                    }
                } catch (Exception e) {
                    log.error("查询药品追溯码任务明细异常: {}", e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 增强处方信息数据，关联药品追溯码任务状态
     *
     * @param dataList 处方信息列表
     */
    private void enhanceDrugDispenseInfosWithTaskStatus(List<DrugDispenseInfoResponse> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }

        // 处理每条处方数据
        Iterator<DrugDispenseInfoResponse> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            DrugDispenseInfoResponse dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getOutPresId();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }

            // 1. 查询是否存在已完成且未删除的任务
            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                    .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
            Integer completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            // 2. 如果存在已完成的任务，则过滤掉该处方数据
            if (completedTaskCount > 0) {
                iterator.remove();
                continue;
            }

            // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                    .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                    .orderByDesc(YsfStoTcTask::getCreateTime); // 按创建时间降序

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
            if (latestTask != null) {
                // 使用实体类中新增的字段
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ?
                        latestTask.getIdTask()
                                .toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ?
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(latestTask.getCreateTime()) : null);
            }
        }
    }

    /**
     * 上传药品追溯码信息
     *
     * @param request 追溯码上传请求
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public R<TraceabilityUploadResultVo> uploadScans(TraceabilityUploadDto request) {
        // 获取当前用户信息
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
        String userId = String.valueOf(userInfo.getUser()
                .getUserId());
        String userName = userInfo.getUser()
                .getUserName();
        String orgId = userInfo.getUser()
                .getOrgId();
        String orgName = userInfo.getUser()
                .getOrgName();

        // 初始化结果变量
        List<String> successIds = new ArrayList<>();
        List<String> failIds = new ArrayList<>();
        Map<String, String> failMessages = new HashMap<>();

        // 校验请求参数
        if (request == null || request.getPrescriptions() == null || request.getPrescriptions()
                .isEmpty()) {
            return R.fail("请求参数不能为空");
        }

        try {
            // 遍历每个处方
            for (TraceabilityUploadDto.PrescriptionItem prescription : request.getPrescriptions()) {
                String outPresId = prescription.getOutPresId();
                if (StringUtils.isEmpty(outPresId)) {
                    failIds.add("unknown");
                    failMessages.put("unknown", "处方ID不能为空");
                    continue;
                }

                // 处理单个处方
                try {
                    processPrescription(prescription, userId, userName, orgId, orgName);
                    successIds.add(outPresId);
                } catch (Exception e) {
                    log.error("处理处方[{}]追溯码失败: {}", outPresId, e.getMessage(), e);
                    failIds.add(outPresId);
                    failMessages.put(outPresId, e.getMessage());
                }
            }

            // 构造返回结果
            TraceabilityUploadResultVo result = new TraceabilityUploadResultVo();
            result.setSuccess(successIds);
            result.setFail(failIds);
            result.setFailMessages(failMessages);

            if (failIds.isEmpty()) {
                return R.ok(result);
            } else {
                R<TraceabilityUploadResultVo> response = R.fail("部分处方追溯码上传失败");
                response.setData(result);
                return response;
            }
        } catch (Exception e) {
            log.error("上传药品追溯码失败: {}", e.getMessage(), e);
            throw new ServiceException("上传药品追溯码失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个处方的追溯码上传
     *
     * @param prescription 处方信息
     * @param userId       用户ID
     * @param userName     用户名
     * @param orgId        机构ID
     * @param orgName      机构名称
     */
    private void processPrescription(TraceabilityUploadDto.PrescriptionItem prescription,
                                     String userId, String userName, String orgId, String orgName) {
        String outPresId = prescription.getOutPresId();

        // 1. 获取或创建发药单
        YsfStoDps stoDps = getOrCreateStoDps(outPresId, prescription, userId, userName, orgId, orgName);

        // 2. 获取或创建扫码任务
        YsfStoTcTask tcTask = getOrCreateTcTask(outPresId, stoDps, userId, userName, orgId, orgName);

        // 3. 更新发药单关联的任务ID
        boolean needUpdate = false;
        if (stoDps.getIdTask() == null) {
            needUpdate = true;
        } else if (!stoDps.getIdTask()
                .equals(tcTask.getIdTask())) {
            needUpdate = true;
        }

        if (needUpdate) {
            stoDps.setIdTask(tcTask.getIdTask());
            ysfStoDpsService.updateById(stoDps);
        }

        // 4. 处理每个药品明细的追溯码
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            processdrugItem(drugItem, stoDps, tcTask, userId, userName, orgId, orgName);
        }

        // 5. 根据发药单的send_time字段决定是否更新状态
        if (stoDps.getSendTime() != null) {
            // 如果已有发药时间，更新发药单和任务状态为已完成
            stoDps.setFgStatus(DispenseOrderStatusEnum.DISPENSED.getCode());
            ysfStoDpsService.updateById(stoDps);

            tcTask.setFgStatus(TaskStatusEnum.COMPLETED.getCode());
            ysfStoTcTaskMapper.updateById(tcTask);
        }
    }

    /**
     * 获取或创建发药单
     */
    private YsfStoDps getOrCreateStoDps(String outPresId, TraceabilityUploadDto.PrescriptionItem prescription,
                                        String userId, String userName, String orgId, String orgName) {
        // 查询是否存在发药单
        LambdaQueryWrapper<YsfStoDps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDps::getCfxh, outPresId)
                .eq(YsfStoDps::getDelFlag, "0");
        YsfStoDps stoDps = ysfStoDpsService.getOne(queryWrapper);

        if (stoDps == null) {
            // 创建新的发药单
            stoDps = new YsfStoDps();
            stoDps.setCfxh(outPresId);
            stoDps.setSdDps("2"); // 处方单
            stoDps.setPatientId(prescription.getPatId());
            stoDps.setPsnName(prescription.getPatName());
            stoDps.setCardNo(prescription.getCardNo());
            stoDps.setFgStatus(DispenseOrderStatusEnum.PENDING.getCode()); // 待发药
            stoDps.setFgDps("0"); // 发药单
            stoDps.setFgPrint("0"); // 未打印
            stoDps.setIdDept(prescription.getIdDept());

            // 设置机构信息
            stoDps.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
            stoDps.setOrgId(orgId);
            stoDps.setOrgName(orgName);

            // 设置创建和修改信息
            stoDps.setCreateBy(userName);
            stoDps.setCreateTime(new Date());
            stoDps.setUpdateBy(userName);
            stoDps.setUpdateTime(new Date());
            stoDps.setDelFlag("0");

            // 插入数据库
            ysfStoDpsService.save(stoDps);
        }

        return stoDps;
    }

    /**
     * 获取或创建扫码任务
     */
    private YsfStoTcTask getOrCreateTcTask(String outPresId, YsfStoDps stoDps,
                                           String userId, String userName, String orgId, String orgName) {
        YsfStoTcTask tcTask = null;

        // 如果发药单已关联任务ID，则查询任务
        if (stoDps.getIdTask() != null) {
            LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(YsfStoTcTask::getIdTask, stoDps.getIdTask())
                    .eq(YsfStoTcTask::getDelFlag, "0");
            tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

            // 如果任务存在且状态为待处理，直接复用
            if (tcTask != null && TaskStatusEnum.PENDING.getCode()
                    .equals(tcTask.getFgStatus())) {
                return tcTask;
            }
        }

        // 查询是否有其他待处理任务
        LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTask::getCdBiz, outPresId)
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理
                .eq(YsfStoTcTask::getDelFlag, "0")
                .orderByDesc(YsfStoTcTask::getCreateTime);
        tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

        if (tcTask == null) {
            // 创建新的扫码任务
            tcTask = new YsfStoTcTask();
            tcTask.setCdBiz(outPresId);
            tcTask.setSdTaskType("1"); // 主动任务
            tcTask.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode()); // 门诊发药
            tcTask.setFgStatus(TaskStatusEnum.PENDING.getCode()); // 待处理
            tcTask.setFgPriority("1"); // 普通优先级
            tcTask.setIdUser(userId);
            tcTask.setIdDept(stoDps.getIdDept());

            // 设置机构信息
            tcTask.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
            tcTask.setOrgId(orgId);
            tcTask.setOrgName(orgName);

            // 设置创建和修改信息
            tcTask.setCreateBy(userName);
            tcTask.setCreateTime(new Date());
            tcTask.setUpdateBy(userName);
            tcTask.setUpdateTime(new Date());
            tcTask.setDelFlag("0");
            tcTask.setRemark(stoDps.getPsnName());

            // 插入数据库
            ysfStoTcTaskMapper.insert(tcTask);
        }

        return tcTask;
    }

    /**
     * 处理药品明细项
     */
    private void processdrugItem(TraceabilityUploadDto.DrugItem drugItem, YsfStoDps stoDps, YsfStoTcTask tcTask,
                                 String userId, String userName, String orgId, String orgName) {
        String outPresdetailid = drugItem.getOutPresdetailid();
        String drugtracinfo = drugItem.getDrugtracinfoScanned();

        // 拆分追溯码
        String[] drugtracinfoArray = drugtracinfo.split(",");
        int drugtracinfoArrayLength = drugtracinfoArray.length;

        if (StringUtils.isEmpty(outPresdetailid)) {
            throw new ServiceException("处方明细ID不能为空");
        }

        // 1. 获取或创建扫码任务明细
        YsfStoTcTaskSub tcTaskSub = getOrCreateTcTaskSub(tcTask, drugItem, userId, userName, orgId, orgName);

        // 2. 获取或创建发药单明细
        YsfStoDpsSub stoDpsSub = getOrCreateStoDpsSub(stoDps, drugItem, userId, userName, orgId, orgName);

        // 3. 处理追溯码（商品追溯码主表）一个追溯码一个记录
        for (String drugZsm : drugtracinfoArray) {
            processTraceabilityCode(drugZsm, drugItem, stoDpsSub.getId()
                    .toString(), userName, orgId, orgName, tcTask.getIdDept());
        }
        // 4. 更新扫码任务明细信息
        tcTaskSub.setDrugtracinfo(drugtracinfo);
        tcTaskSub.setFgScanned("1"); // 已扫码
        tcTaskSub.setScanUser(userName);
        tcTaskSub.setScanTime(new Date());
        tcTaskSub.setUpdateBy(userName);
        tcTaskSub.setUpdateTime(new Date());
        tcTaskSub.setTracCnt(drugtracinfoArrayLength);
        ysfStoTcTaskSubService.updateById(tcTaskSub);

        // 5. 更新发药单明细
        stoDpsSub.setDrugtracinfo(drugtracinfo);
        stoDpsSub.setUpdateBy(userName);
        stoDpsSub.setUpdateTime(new Date());
        stoDpsSub.setTracCnt(drugtracinfoArrayLength);
        ysfStoDpsSubService.updateById(stoDpsSub);

    }

    /**
     * 获取或创建任务明细
     */
    private YsfStoTcTaskSub getOrCreateTcTaskSub(YsfStoTcTask tcTask, TraceabilityUploadDto.DrugItem drugItem,
                                                 String userId, String userName, String orgId, String orgName) {
        // 查询是否存在任务明细
        YsfStoTcTaskSub tcTaskSub = ysfStoTcTaskSubService.getByTaskIdAndCfmxxh(
                tcTask.getIdTask()
                        .toString(), drugItem.getOutPresdetailid());

        if (tcTaskSub == null) {
            // 创建新的任务明细
            tcTaskSub = new YsfStoTcTaskSub();
            tcTaskSub.setIdTask(tcTask.getIdTask()
                    .toString());
            tcTaskSub.setCfmxxh(drugItem.getOutPresdetailid());
            tcTaskSub.setDrugCode(drugItem.getDrugCode());
            tcTaskSub.setFgScanned("0"); // 未扫码
            tcTaskSub.setDrugtracinfo(drugItem.getDrugtracinfoScanned());
            tcTaskSub.setQuantity(drugItem.getQuantity());
            tcTaskSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            tcTaskSub.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
            tcTaskSub.setOrgId(orgId);
            tcTaskSub.setOrgName(orgName);

            // 设置创建和修改信息
            tcTaskSub.setCreateBy(userName);
            tcTaskSub.setCreateTime(new Date());
            tcTaskSub.setUpdateBy(userName);
            tcTaskSub.setUpdateTime(new Date());
            tcTaskSub.setDelFlag("0");
            tcTaskSub.setRemark(tcTask.getRemark());

            // 插入数据库
            ysfStoTcTaskSubService.save(tcTaskSub);
        }

        return tcTaskSub;
    }

    /**
     * 获取或创建发药单明细
     */
    private YsfStoDpsSub getOrCreateStoDpsSub(YsfStoDps stoDps, TraceabilityUploadDto.DrugItem drugItem,
                                              String userId, String userName, String orgId, String orgName) {
        // 查询是否存在发药单明细
        LambdaQueryWrapper<YsfStoDpsSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDpsSub::getIdDps, stoDps.getIdDps())
                .eq(YsfStoDpsSub::getCfmxxh, drugItem.getOutPresdetailid())
                .eq(YsfStoDpsSub::getDelFlag, "0");
        YsfStoDpsSub stoDpsSub = ysfStoDpsSubService.getOne(queryWrapper);

        if (stoDpsSub == null) {
            // 创建新的发药单明细
            stoDpsSub = new YsfStoDpsSub();
            stoDpsSub.setIdDps(stoDps.getIdDps()
                    .toString());
            stoDpsSub.setCfmxxh(drugItem.getOutPresdetailid());
            stoDpsSub.setCfxh(stoDps.getCfxh());
            stoDpsSub.setDrugCode(drugItem.getDrugCode());
            stoDpsSub.setNaFee(drugItem.getDrugName());
            stoDpsSub.setPriceSale(drugItem.getPrice() != null ? new BigDecimal(drugItem.getPrice()) : null);
            stoDpsSub.setSelRetnCnt(drugItem.getQuantity());
            stoDpsSub.setAmtTotal(drugItem.getAmount() != null ? new BigDecimal(drugItem.getAmount()) : null);
            stoDpsSub.setAmtTotalDps(drugItem.getAmount() != null ? new BigDecimal(drugItem.getAmount()) : null);
            stoDpsSub.setUnitSale(drugItem.getUnit());
            stoDpsSub.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoDpsSub.setQuantity(drugItem.getQuantity());
            stoDpsSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            stoDpsSub.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
            stoDpsSub.setOrgId(orgId);
            stoDpsSub.setOrgName(orgName);

            // 设置创建和修改信息
            stoDpsSub.setCreateBy(userName);
            stoDpsSub.setCreateTime(new Date());
            stoDpsSub.setUpdateBy(userName);
            stoDpsSub.setUpdateTime(new Date());
            stoDpsSub.setDelFlag("0");

            // 插入数据库
            ysfStoDpsSubService.save(stoDpsSub);
        }

        return stoDpsSub;
    }

    /**
     * 处理追溯码
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param userName     用户名
     * @param orgId        组织id
     * @param orgName      组织名称
     * @param idDept       发药部门id
     */
    private void processTraceabilityCode(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                         String stoDpsSubId, String userName, String orgId, String orgName,String idDept) {
        // 查询是否存在追溯码记录
        LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0");
        YsfStoTc stoTc = ysfStoTcService.getOne(queryWrapper);

        if (stoTc != null) {
            // 检查追溯码是否已被使用
            if (stoTc.getAmountRem() != null && stoTc.getAmountRem()
                    .compareTo(BigDecimal.ZERO) == 0) {
                throw new ServiceException("追溯码[" + drugtracinfo + "]已被使用");
            }

            // 更新追溯码记录，将剩余数量设为0
            stoTc.setAmountRem(BigDecimal.ZERO);
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(new Date());
            ysfStoTcService.updateById(stoTc);
        } else {
            // 创建新的追溯码记录
            stoTc = new YsfStoTc();
            stoTc.setDrugtracinfo(drugtracinfo);
            stoTc.setDrugCode(drugItem.getDrugCode());
            stoTc.setAmountRem(BigDecimal.ZERO); // 表示已全部消耗
            stoTc.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoTc.setUnitTc(drugItem.getMinPackingName());
            stoTc.setFgActive("1"); // 有效
            stoTc.setIdDept(idDept);

            // 设置机构信息
            stoTc.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
            stoTc.setOrgId(orgId);
            stoTc.setOrgName(orgName);
            stoTc.setSdTcManage("简易管理");

            // 设置创建和修改信息
            stoTc.setCreateBy(userName);
            stoTc.setCreateTime(new Date());
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(new Date());
            stoTc.setDelFlag("0");

            // 插入数据库
            ysfStoTcService.save(stoTc);
        }

        // 创建追溯码状态记录
        createTraceabilityStatus(drugtracinfo, drugItem, stoDpsSubId, stoTc.getIdTc(), userName, orgId, orgName, idDept);
    }

    /**
     * 创建追溯码状态记录
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param idTc         追溯码id
     * @param userName     用户名
     * @param orgId        组织id
     * @param orgName      组织名称
     * @param idDept       发药部门id
     */
    private void createTraceabilityStatus(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                          String stoDpsSubId, Long idTc, String userName, String orgId, String orgName, String idDept) {
        YsfStoTcStatus tcStatus = new YsfStoTcStatus();
        tcStatus.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode()); // 门诊发药
        tcStatus.setSdTcManage("1"); // 简易管理模式
        tcStatus.setIdBizOri(stoDpsSubId);
        tcStatus.setCfmxxh(drugItem.getOutPresdetailid());
        tcStatus.setDrugCode(drugItem.getDrugCode());
        tcStatus.setDrugtracinfo(drugtracinfo);
        tcStatus.setSdTc("2"); // 商品追溯码
        tcStatus.setSelRetnCnt(drugItem.getMinDoseCount());
        tcStatus.setFgUp("0"); // 未上传
        tcStatus.setFgActive("1"); // 有效
        tcStatus.setIdDept(idDept);
        tcStatus.setIdTc(idTc);

        // 设置机构信息
        tcStatus.setIdOrg(MsunYingDongUtil.MEDICAL_CODE);
        tcStatus.setOrgId(orgId);
        tcStatus.setOrgName(orgName);

        // 设置创建和修改信息
        tcStatus.setCreateBy(userName);
        tcStatus.setCreateTime(new Date());
        tcStatus.setUpdateBy(userName);
        tcStatus.setUpdateTime(new Date());
        tcStatus.setDelFlag("0");

        // 插入数据库
        ysfStoTcStatusService.save(tcStatus);
    }

    /**
     * 根据条件查询发药单列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单列表, 包含 DispensingRecordVo 对象
     */
    public TableDataInfo queryDispensingRecords(DispensingRecordQueryDto queryDto) {
        // 2. 使用 PageHelper.startPage 进行分页
        PageHelper.startPage(
                queryDto.getPageNum() == null ? 1 : queryDto.getPageNum(),
                queryDto.getPageSize() == null ? 10 : queryDto.getPageSize()
        );

        // 3. 构建查询条件 (MyBatis LambdaQueryWrapper)
        LambdaQueryWrapper<YsfStoDps> dpsQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDto.getPatientName())) {
            dpsQueryWrapper.like(YsfStoDps::getPsnName, queryDto.getPatientName());
        }
        if (StringUtils.isNotEmpty(queryDto.getPrescriptionId())) {
            // 根据需求文档，处方号可以对应 outPresId 或 cfxh。
            // YsfStoDps 实体中似乎只有 cfxh。如果 outPresId 是另一个表的字段或不同含义，需确认。
            // 这里我们假设 DTO 中的 prescriptionId 直接对应 YsfStoDps 的 cfxh。
            dpsQueryWrapper.eq(YsfStoDps::getCfxh, queryDto.getPrescriptionId());
        }
        if (StringUtils.isNotEmpty(queryDto.getDispensingStatus())) {
            dpsQueryWrapper.eq(YsfStoDps::getFgStatus, queryDto.getDispensingStatus());
        }
        if (StringUtils.isNotEmpty(queryDto.getDeptId())) {
            dpsQueryWrapper.eq(YsfStoDps::getIdDept, queryDto.getDeptId());
        }
        if (queryDto.getStartTime() != null) {
            dpsQueryWrapper.ge(YsfStoDps::getSendTime, queryDto.getStartTime());
        }
        if (queryDto.getEndTime() != null) {
            // 对于日期范围查询，通常结束时间应该包含当天，所以可以用小于第二天的开始
            // 或者数据库层面处理 DATE(send_time) <= DATE(queryDto.getEndTime())
            // 为简单起见，这里用 le，如果需要精确到秒，前端传入的时间应为当天的 23:59:59
            dpsQueryWrapper.le(YsfStoDps::getSendTime, queryDto.getEndTime());
        }
        dpsQueryWrapper.eq(YsfStoDps::getDelFlag, "0"); // 通常查询未删除的记录
        dpsQueryWrapper.orderByDesc(YsfStoDps::getSendTime, YsfStoDps::getIdDps); // 按发药时间降序, ID降序作为次排序

        // 4. 查询 ysf_sto_dps 获取主要信息
        List<YsfStoDps> dpsList = ysfStoDpsService.list(dpsQueryWrapper);

        // 5. 获取总记录数 (PageHelper 会处理)
        // 6. 如果 dpsList 为空，直接返回
        if (CollectionUtils.isEmpty(dpsList)) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }

        PageInfo<YsfStoDps> pageInfo = new PageInfo<>(dpsList);

        // 7. 遍历 dpsList，对每个 YsfStoDps 对象进行处理和转换:
        List<DispensingRecordVo> voList = pageInfo.getList()
                .stream()
                .map(dps -> {

                    DispensingRecordVo vo = new DispensingRecordVo();

                    BeanUtils.copyProperties(dps, vo);

                    vo.setDispensingFgStatus(dps.getFgStatus());

                    if (StringUtils.isNotEmpty(dps.getCfxh())) {
                        LambdaQueryWrapper<YsfStoTcTask> taskQuery = new LambdaQueryWrapper<>();
                        taskQuery.eq(YsfStoTcTask::getCdBiz, dps.getCfxh());
                        taskQuery.eq(YsfStoTcTask::getDelFlag, "0");
                        taskQuery.orderByDesc(YsfStoTcTask::getCreateTime);
                        taskQuery.last("LIMIT 1");
                        YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(taskQuery);
                        if (latestTask != null) {
                            vo.setTaskStatus(latestTask.getFgStatus());
                            vo.setTaskId(latestTask.getIdTask());
                        }
                    }

                    return vo;
                })
                .collect(Collectors.toList());
        return new TableDataInfo(voList, (int) pageInfo.getTotal());
    }

    /**
     * 根据条件查询发药单明细列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单明细列表
     */
    public TableDataInfo queryDispensingRecordDetails(DispensingRecordDetailQueryDto queryDto) {
        // 使用 PageHelper.startPage 进行分页
        PageHelper.startPage(
                queryDto.getPageNum() == null ? 1 : queryDto.getPageNum(),
                queryDto.getPageSize() == null ? 10 : queryDto.getPageSize()
        );

        

        // 查询发药单明细
        List<YsfStoDpsSub> subList = ysfStoDpsSubMapper.queryDispensingRecordDetailsWithConditions(queryDto);
        if (CollectionUtils.isEmpty(subList)) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }

        // 查询当前活动任务
        LambdaQueryWrapper<YsfStoTcTask> taskQuery = new LambdaQueryWrapper<>();
        // 获取发药单的处方号
        String cfxh = "";
        if (!subList.isEmpty()) {
            cfxh = subList.get(0)
                    .getCfxh();
        }

        taskQuery.eq(YsfStoTcTask::getCdBiz, cfxh)
                .eq(YsfStoTcTask::getDelFlag, "0")
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理的任务
                .orderByDesc(YsfStoTcTask::getCreateTime)
                .last("LIMIT 1");

        YsfStoTcTask currentTask = ysfStoTcTaskMapper.selectOne(taskQuery);

        // 获取任务明细信息
        Map<String, YsfStoTcTaskSub> subTaskMap = new HashMap<>();
        if (currentTask != null) {
            LambdaQueryWrapper<YsfStoTcTaskSub> taskSubQuery = new LambdaQueryWrapper<>();
            taskSubQuery.eq(YsfStoTcTaskSub::getIdTask, currentTask.getIdTask()
                            .toString())
                    .eq(YsfStoTcTaskSub::getDelFlag, "0");

            // 如果需要过滤已采集状态
            if (StringUtils.isNotEmpty(queryDto.getIsCollected())) {
                taskSubQuery.eq(YsfStoTcTaskSub::getFgScanned, queryDto.getIsCollected());
            }

            List<YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.list(taskSubQuery);
            taskSubList.forEach(sub -> {
                if (StringUtils.isNotEmpty(sub.getIdBizSub())) {
                    subTaskMap.put(sub.getIdBizSub(), sub);
                }
            });
        }

        // 转换为VO对象
        List<DispensingRecordDetailVo> voList = subList.stream()
                .map(sub -> {
                    DispensingRecordDetailVo vo = new DispensingRecordDetailVo();
                    BeanUtils.copyProperties(sub, vo);

                    // 设置任务明细信息
                    YsfStoTcTaskSub taskSub = subTaskMap.get(sub.getId()
                            .toString());
                    if (taskSub != null) {
                        vo.setIdSub(taskSub.getIdSub());
                        vo.setFgScanned(taskSub.getFgScanned());
                    } else {
                        vo.setFgScanned("0"); // 默认未扫码
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        // 如果指定了是否已采集条件，但没有活动任务，则根据条件过滤结果
        if (StringUtils.isNotEmpty(queryDto.getIsCollected()) && currentTask == null) {
            if ("1".equals(queryDto.getIsCollected())) {
                // 如果查询已采集但没有任务，返回空列表
                return new TableDataInfo(Collections.emptyList(), 0);
            }
        }

        return new TableDataInfo(voList, (int) pageInfo.getTotal());
    }

    /**
     * 取消扫码任务
     *
     * @param taskId 任务id
     * @return {@link AjaxResult }
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cancelTask(Long taskId) {
        // 查询ysf_sto_tc_task记录，确认状态为'0'(待处理)
        YsfStoTcTask task = ysfStoTcTaskService.getById(taskId);
        if (task == null) {
            return AjaxResult.error("任务不存在");
        }

        if (!StoTcTaskStatusEnum.PENDING.getCode()
                .equals(task.getFgStatus())) {
            return AjaxResult.error("只能取消待处理状态的任务");
        }

        // 更新ysf_sto_tc_task状态为'2'(已失效)，并添加备注
        task.setFgStatus(StoTcTaskStatusEnum.EXPIRED.getCode());
        task.setMemo("操作员取消，发药前病人未取药");
        ysfStoTcTaskService.updateById(task);

        // 处理已扫描的追溯码ysf_sto_tc
        // 根据被取消的id_task，查询ysf_sto_tc_task_sub获取所有已扫描的drugtracinfo
        LambdaQueryWrapper<YsfStoTcTaskSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTaskSub::getIdTask, taskId.toString())
                .eq(YsfStoTcTaskSub::getFgScanned, "1")  // 只处理已扫描的明细
                .eq(YsfStoTcTaskSub::getDelFlag, "0");   // 未删除的记录
        List<YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.list(queryWrapper);

        if (!taskSubList.isEmpty()) {
            for (YsfStoTcTaskSub taskSub : taskSubList) {
                String drugtracinfo = taskSub.getDrugtracinfo();

                // 将追溯码可用数量恢复为其包装数量
                LambdaQueryWrapper<YsfStoTc> tcWrapper = new LambdaQueryWrapper<>();
                tcWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                        .eq(YsfStoTc::getDelFlag, "0");
                YsfStoTc stoTc = ysfStoTcService.getOne(tcWrapper);

                if (stoTc != null) {
                    // 恢复其包装数量
                    stoTc.setAmountRem(new BigDecimal(stoTc.getUnitSaleFactor()));
                    ysfStoTcService.updateById(stoTc);

                    // 创建ysf_sto_tc_status记录
                    YsfStoTcStatus tcStatus = new YsfStoTcStatus();
                    tcStatus.setSdTcStatus(SdTcStatusEnum.CANCEL_TASK.getCode());  // 扫码任务取消
                    tcStatus.setSdTcManage(stoTc.getSdTcManage());
                    tcStatus.setIdBizOri(taskSub.getIdSub()
                            .toString());  // 关联任务明细ID
                    tcStatus.setIdTc(stoTc.getIdTc());
                    tcStatus.setCfmxxh(taskSub.getCfmxxh());
                    tcStatus.setDrugCode(taskSub.getDrugCode());
                    tcStatus.setDrugtracinfo(drugtracinfo);
                    tcStatus.setIdStoInv(stoTc.getIdStoInv());
                    tcStatus.setIdDept(stoTc.getIdDept());
                    tcStatus.setSdTc(stoTc.getSdTc());
                    tcStatus.setSelRetnCnt(stoTc.getUnitSaleFactor());  // 恢复的数量
                    tcStatus.setFgActive("1");
                    tcStatus.setIdOrg(stoTc.getIdOrg());
                    tcStatus.setOrgId(stoTc.getOrgId());
                    tcStatus.setOrgName(stoTc.getOrgName());
                    tcStatus.setSdTc("2");

                    ysfStoTcStatusService.save(tcStatus);
                }
            }
        }

        return AjaxResult.success("取消任务成功");
    }
}